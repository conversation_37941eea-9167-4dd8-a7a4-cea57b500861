import mysql from 'mysql2/promise';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

dotenv.config();

const importMockData = async () => {
  let connection;
  
  try {
    console.log('🔄 连接到MySQL数据库...');
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'redhat',
      charset: 'utf8mb4'
    });

    console.log('✅ 数据库连接成功');

    // 首先创建所有必要的表
    console.log('🔄 创建数据库表...');

    // 创建学院表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS colleges (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // 创建专业表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS majors (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        college_id VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE CASCADE,
        UNIQUE KEY unique_major_college (name, college_id)
      )
    `);

    // 创建宿舍楼表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dorm_buildings (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        floors INT NOT NULL,
        total_rooms INT NOT NULL,
        assigned_admin_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // 创建用户表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(191) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        role ENUM('系统管理员', '宿舍管理员', '学生', '维修人员') NOT NULL,
        college_id VARCHAR(50),
        major_id VARCHAR(50),
        dorm_building_id VARCHAR(50),
        room_number VARCHAR(20),
        emergency_contact_name VARCHAR(100),
        emergency_contact_phone VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE SET NULL,
        FOREIGN KEY (major_id) REFERENCES majors(id) ON DELETE SET NULL,
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE SET NULL
      )
    `);

    // 创建房间表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS rooms (
        id VARCHAR(50) PRIMARY KEY,
        room_number VARCHAR(20) NOT NULL,
        dorm_building_id VARCHAR(50) NOT NULL,
        floor INT NOT NULL,
        type ENUM('单人间', '双人间', '四人间', '六人间') NOT NULL,
        capacity INT NOT NULL,
        occupied_beds INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
        UNIQUE KEY unique_room_building (room_number, dorm_building_id)
      )
    `);

    // 创建床位表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS beds (
        id VARCHAR(50) PRIMARY KEY,
        room_id VARCHAR(50) NOT NULL,
        bed_number VARCHAR(10) NOT NULL,
        status ENUM('空闲', '已入住') DEFAULT '空闲',
        student_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE SET NULL,
        UNIQUE KEY unique_bed_room (bed_number, room_id)
      )
    `);

    // 创建维修请求表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS repair_requests (
        id VARCHAR(50) PRIMARY KEY,
        student_id VARCHAR(50) NOT NULL,
        room_id VARCHAR(50) NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT NOT NULL,
        status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消') DEFAULT '待处理',
        assigned_staff_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
        FOREIGN KEY (assigned_staff_id) REFERENCES users(id) ON DELETE SET NULL
      )
    `);

    // 创建公告表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS announcements (
        id VARCHAR(50) PRIMARY KEY,
        title VARCHAR(200) NOT NULL,
        content TEXT NOT NULL,
        author_id VARCHAR(50) NOT NULL,
        target_roles JSON,
        is_urgent BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    // 创建水电费账单表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS utility_bills (
        id VARCHAR(50) PRIMARY KEY,
        student_id VARCHAR(50) NOT NULL,
        room_id VARCHAR(50) NOT NULL,
        month VARCHAR(7) NOT NULL,
        electricity_usage DECIMAL(10,2) NOT NULL,
        electricity_cost DECIMAL(10,2) NOT NULL,
        water_usage DECIMAL(10,2) NOT NULL,
        water_cost DECIMAL(10,2) NOT NULL,
        total_cost DECIMAL(10,2) NOT NULL,
        is_paid BOOLEAN DEFAULT FALSE,
        paid_at TIMESTAMP NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
        UNIQUE KEY unique_student_month (student_id, month)
      )
    `);

    // 创建违规记录表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS violations (
        id VARCHAR(50) PRIMARY KEY,
        student_id VARCHAR(50) NOT NULL,
        dorm_building_id VARCHAR(50) NOT NULL,
        date DATE NOT NULL,
        type VARCHAR(100) NOT NULL,
        description TEXT NOT NULL,
        action_taken TEXT,
        recorded_by VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
        FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    // 创建晚归记录表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS late_returns (
        id VARCHAR(50) PRIMARY KEY,
        student_id VARCHAR(50) NOT NULL,
        dorm_building_id VARCHAR(50) NOT NULL,
        date DATE NOT NULL,
        time TIME NOT NULL,
        reason TEXT NOT NULL,
        recorded_by VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
        FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    // 创建访客记录表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS visitors (
        id VARCHAR(50) PRIMARY KEY,
        visitor_name VARCHAR(100) NOT NULL,
        visitor_id_number VARCHAR(50),
        reason TEXT NOT NULL,
        entry_time TIMESTAMP NOT NULL,
        exit_time TIMESTAMP NULL,
        visited_student_id VARCHAR(50) NOT NULL,
        dorm_building_id VARCHAR(50) NOT NULL,
        recorded_by VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (visited_student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
        FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    // 创建文明宿舍评分表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS civilized_dorm_scores (
        id VARCHAR(50) PRIMARY KEY,
        dorm_building_id VARCHAR(50) NOT NULL,
        room_id VARCHAR(50) NOT NULL,
        date DATE NOT NULL,
        score INT NOT NULL CHECK (score >= 0 AND score <= 100),
        notes TEXT,
        recorded_by VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
        FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
        FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    console.log('✅ 数据库表创建完成');

    // 清空现有数据（按依赖关系顺序）
    console.log('🔄 清空现有数据...');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 0');
    await connection.execute('TRUNCATE TABLE civilized_dorm_scores');
    await connection.execute('TRUNCATE TABLE visitors');
    await connection.execute('TRUNCATE TABLE late_returns');
    await connection.execute('TRUNCATE TABLE violations');
    await connection.execute('TRUNCATE TABLE utility_bills');
    await connection.execute('TRUNCATE TABLE announcements');
    await connection.execute('TRUNCATE TABLE repair_requests');
    await connection.execute('TRUNCATE TABLE beds');
    await connection.execute('TRUNCATE TABLE rooms');
    await connection.execute('TRUNCATE TABLE users');
    await connection.execute('TRUNCATE TABLE dorm_buildings');
    await connection.execute('TRUNCATE TABLE majors');
    await connection.execute('TRUNCATE TABLE colleges');
    await connection.execute('SET FOREIGN_KEY_CHECKS = 1');

    console.log('🔄 开始导入模拟数据...');

    // 导入学院数据
    console.log('📚 导入学院数据...');
    const colleges = [
      { id: 'col01', name: '工程学院' },
      { id: 'col02', name: '文理学院' },
      { id: 'col03', name: '商学院' },
    ];

    for (const college of colleges) {
      await connection.execute(
        'INSERT INTO colleges (id, name) VALUES (?, ?)',
        [college.id, college.name]
      );
    }

    // 导入专业数据
    console.log('🎓 导入专业数据...');
    const majors = [
      { id: 'maj01', name: '计算机科学', collegeId: 'col01' },
      { id: 'maj02', name: '机械工程', collegeId: 'col01' },
      { id: 'maj03', name: '历史学', collegeId: 'col02' },
      { id: 'maj04', name: '文学', collegeId: 'col02' },
      { id: 'maj05', name: '金融学', collegeId: 'col03' },
    ];

    for (const major of majors) {
      await connection.execute(
        'INSERT INTO majors (id, name, college_id) VALUES (?, ?, ?)',
        [major.id, major.name, major.collegeId]
      );
    }

    // 导入宿舍楼数据
    console.log('🏢 导入宿舍楼数据...');
    const dormBuildings = [
      { id: 'bldgA', name: 'A栋 (阿尔法楼)', floors: 5, totalRooms: 50, assignedAdminId: null },
      { id: 'bldgB', name: 'B栋 (贝塔公寓)', floors: 4, totalRooms: 40, assignedAdminId: null },
      { id: 'bldgC', name: 'C栋 (伽马学舍)', floors: 6, totalRooms: 60, assignedAdminId: null },
    ];

    for (const building of dormBuildings) {
      await connection.execute(
        'INSERT INTO dorm_buildings (id, name, floors, total_rooms, assigned_admin_id) VALUES (?, ?, ?, ?, ?)',
        [building.id, building.name, building.floors, building.totalRooms, building.assignedAdminId]
      );
    }

    // 导入用户数据
    console.log('👥 导入用户数据...');
    const hashedPassword = await bcrypt.hash('password123', 12);

    const users = [
      {
        id: 'sysadmin01',
        name: '系统管理员用户',
        email: '<EMAIL>',
        password: hashedPassword,
        role: '系统管理员',
        phone: null,
        collegeId: null,
        majorId: null,
        dormBuildingId: null,
        roomNumber: null,
        emergencyContactName: null,
        emergencyContactPhone: null
      },
      {
        id: 'dormadmin01',
        name: '张三 (A栋宿舍管理员)',
        email: '<EMAIL>',
        password: hashedPassword,
        role: '宿舍管理员',
        phone: null,
        collegeId: null,
        majorId: null,
        dormBuildingId: 'bldgA',
        roomNumber: null,
        emergencyContactName: null,
        emergencyContactPhone: null
      },
      {
        id: 'dormadmin02',
        name: '李四 (B栋宿舍管理员)',
        email: '<EMAIL>',
        password: hashedPassword,
        role: '宿舍管理员',
        phone: null,
        collegeId: null,
        majorId: null,
        dormBuildingId: 'bldgB',
        roomNumber: null,
        emergencyContactName: null,
        emergencyContactPhone: null
      },
      {
        id: 'student01',
        name: '王五',
        email: '<EMAIL>',
        password: hashedPassword,
        role: '学生',
        phone: '13000000001',
        collegeId: 'col01',
        majorId: 'maj01',
        dormBuildingId: 'bldgA',
        roomNumber: '101',
        emergencyContactName: '王大锤',
        emergencyContactPhone: '13800138000'
      },
      {
        id: 'student02',
        name: '赵六',
        email: '<EMAIL>',
        password: hashedPassword,
        role: '学生',
        phone: '13000000002',
        collegeId: 'col02',
        majorId: 'maj03',
        dormBuildingId: 'bldgB',
        roomNumber: '205',
        emergencyContactName: '赵铁柱',
        emergencyContactPhone: '13900139000'
      },
      {
        id: 'student03',
        name: '孙七',
        email: '<EMAIL>',
        password: hashedPassword,
        role: '学生',
        phone: '13000000003',
        collegeId: 'col01',
        majorId: 'maj02',
        dormBuildingId: 'bldgA',
        roomNumber: '102',
        emergencyContactName: '孙悟空',
        emergencyContactPhone: '13700137000'
      },
      {
        id: 'repair01',
        name: '维修工丁师傅',
        email: '<EMAIL>',
        password: hashedPassword,
        role: '维修人员',
        phone: null,
        collegeId: null,
        majorId: null,
        dormBuildingId: null,
        roomNumber: null,
        emergencyContactName: null,
        emergencyContactPhone: null
      },
    ];

    for (const user of users) {
      await connection.execute(`
        INSERT INTO users (
          id, name, email, password, phone, role,
          college_id, major_id, dorm_building_id, room_number,
          emergency_contact_name, emergency_contact_phone
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        user.id, user.name, user.email, user.password, user.phone, user.role,
        user.collegeId, user.majorId, user.dormBuildingId, user.roomNumber,
        user.emergencyContactName, user.emergencyContactPhone
      ]);
    }

    // 更新宿舍楼管理员分配
    await connection.execute('UPDATE dorm_buildings SET assigned_admin_id = ? WHERE id = ?', ['dormadmin01', 'bldgA']);
    await connection.execute('UPDATE dorm_buildings SET assigned_admin_id = ? WHERE id = ?', ['dormadmin02', 'bldgB']);

    // 导入房间数据
    console.log('🏠 导入房间数据...');
    const rooms = [
      { id: 'roomA101', roomNumber: '101', dormBuildingId: 'bldgA', floor: 1, type: '六人间', capacity: 6, occupiedBeds: 1 },
      { id: 'roomA102', roomNumber: '102', dormBuildingId: 'bldgA', floor: 1, type: '双人间', capacity: 2, occupiedBeds: 1 },
      { id: 'roomB205', roomNumber: '205', dormBuildingId: 'bldgB', floor: 2, type: '六人间', capacity: 6, occupiedBeds: 1 },
      { id: 'roomC301', roomNumber: '301', dormBuildingId: 'bldgC', floor: 3, type: '单人间', capacity: 1, occupiedBeds: 0 },
    ];

    for (const room of rooms) {
      await connection.execute(`
        INSERT INTO rooms (id, room_number, dorm_building_id, floor, type, capacity, occupied_beds)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [room.id, room.roomNumber, room.dormBuildingId, room.floor, room.type, room.capacity, room.occupiedBeds]);
    }

    // 导入床位数据
    console.log('🛏️ 导入床位数据...');
    const beds = [
      // Room A101 (六人间, student01 在床位1)
      { id: 'bedA101-1', roomId: 'roomA101', bedNumber: '1', status: '已入住', studentId: 'student01' },
      { id: 'bedA101-2', roomId: 'roomA101', bedNumber: '2', status: '空闲', studentId: null },
      { id: 'bedA101-3', roomId: 'roomA101', bedNumber: '3', status: '空闲', studentId: null },
      { id: 'bedA101-4', roomId: 'roomA101', bedNumber: '4', status: '空闲', studentId: null },
      { id: 'bedA101-5', roomId: 'roomA101', bedNumber: '5', status: '空闲', studentId: null },
      { id: 'bedA101-6', roomId: 'roomA101', bedNumber: '6', status: '空闲', studentId: null },

      // Room A102 (双人间, student03 在床位1)
      { id: 'bedA102-1', roomId: 'roomA102', bedNumber: '1', status: '已入住', studentId: 'student03' },
      { id: 'bedA102-2', roomId: 'roomA102', bedNumber: '2', status: '空闲', studentId: null },

      // Room B205 (六人间, student02 在床位1)
      { id: 'bedB205-1', roomId: 'roomB205', bedNumber: '1', status: '已入住', studentId: 'student02' },
      { id: 'bedB205-2', roomId: 'roomB205', bedNumber: '2', status: '空闲', studentId: null },
      { id: 'bedB205-3', roomId: 'roomB205', bedNumber: '3', status: '空闲', studentId: null },
      { id: 'bedB205-4', roomId: 'roomB205', bedNumber: '4', status: '空闲', studentId: null },
      { id: 'bedB205-5', roomId: 'roomB205', bedNumber: '5', status: '空闲', studentId: null },
      { id: 'bedB205-6', roomId: 'roomB205', bedNumber: '6', status: '空闲', studentId: null },

      // Room C301 (单人间, 空闲)
      { id: 'bedC301-1', roomId: 'roomC301', bedNumber: '1', status: '空闲', studentId: null },
    ];

    for (const bed of beds) {
      await connection.execute(`
        INSERT INTO beds (id, room_id, bed_number, status, student_id)
        VALUES (?, ?, ?, ?, ?)
      `, [bed.id, bed.roomId, bed.bedNumber, bed.status, bed.studentId]);
    }

    // 导入维修请求数据
    console.log('🔧 导入维修请求数据...');
    const repairRequests = [
      {
        id: 'repair001',
        studentId: 'student01',
        roomId: 'roomA101',
        title: '卫生间水龙头漏水',
        description: '卫生间水龙头漏水严重，无法关闭。',
        status: '已指派',
        assignedStaffId: 'repair01',
        createdAt: '2024-07-20 10:00:00'
      },
      {
        id: 'repair002',
        studentId: 'student02',
        roomId: 'roomB205',
        title: '书桌台灯不亮',
        description: '书桌台灯不亮，更换灯泡无效。',
        status: '待处理',
        assignedStaffId: null,
        createdAt: '2024-07-21 14:30:00'
      },
      {
        id: 'repair003',
        studentId: 'student03',
        roomId: 'roomA102',
        title: '空调制冷效果差',
        description: '空调制冷效果差，噪音大。',
        status: '维修中',
        assignedStaffId: 'repair01',
        createdAt: '2024-07-22 08:15:00'
      },
      {
        id: 'repair004',
        studentId: 'student01',
        roomId: 'roomA101',
        title: '窗户卡住',
        description: '窗户卡住，无法完全关闭。',
        status: '已完成',
        assignedStaffId: 'repair01',
        createdAt: '2024-07-19 16:00:00',
        completedAt: '2024-07-20 09:30:00'
      }
    ];

    for (const request of repairRequests) {
      await connection.execute(`
        INSERT INTO repair_requests (id, student_id, room_id, title, description, status, assigned_staff_id, created_at, completed_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        request.id, request.studentId, request.roomId, request.title, request.description,
        request.status, request.assignedStaffId, request.createdAt, request.completedAt || null
      ]);
    }

    // 导入公告数据
    console.log('📢 导入公告数据...');
    const announcements = [
      {
        id: 'anno001',
        title: '近期维修安排通知',
        content: '请注意，A栋将于7月25日上午9点至下午5点进行计划内维修。',
        authorId: 'sysadmin01',
        targetRoles: JSON.stringify(['学生', '宿舍管理员']),
        isUrgent: false,
        createdAt: '2024-07-19 08:00:00'
      },
      {
        id: 'anno002',
        title: '宿舍会议 - B栋',
        content: 'B栋全体住宿学生务必参加7月26日晚上7点在公共活动室举行的会议。',
        authorId: 'dormadmin02',
        targetRoles: JSON.stringify(['学生']),
        isUrgent: false,
        createdAt: '2024-07-20 11:00:00'
      }
    ];

    for (const announcement of announcements) {
      await connection.execute(`
        INSERT INTO announcements (id, title, content, author_id, target_roles, is_urgent, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        announcement.id, announcement.title, announcement.content, announcement.authorId,
        announcement.targetRoles, announcement.isUrgent, announcement.createdAt
      ]);
    }

    // 导入水电费账单数据
    console.log('💰 导入水电费账单数据...');
    const utilityBills = [
      {
        id: 'bill001',
        studentId: 'student01',
        roomId: 'roomA101',
        month: '2024-06',
        electricityUsage: 50.0,
        electricityCost: 25.0,
        waterUsage: 5.0,
        waterCost: 10.0,
        totalCost: 35.0,
        isPaid: true,
        paidAt: '2024-07-05 10:30:00'
      },
      {
        id: 'bill002',
        studentId: 'student02',
        roomId: 'roomB205',
        month: '2024-06',
        electricityUsage: 60.0,
        electricityCost: 30.0,
        waterUsage: 6.0,
        waterCost: 12.0,
        totalCost: 42.0,
        isPaid: false,
        paidAt: null
      },
      {
        id: 'bill003',
        studentId: 'student01',
        roomId: 'roomA101',
        month: '2024-05',
        electricityUsage: 45.0,
        electricityCost: 22.5,
        waterUsage: 4.0,
        waterCost: 8.0,
        totalCost: 30.5,
        isPaid: false,
        paidAt: null
      },
      {
        id: 'bill004',
        studentId: 'student03',
        roomId: 'roomA102',
        month: '2024-06',
        electricityUsage: 30.0,
        electricityCost: 15.0,
        waterUsage: 3.0,
        waterCost: 6.0,
        totalCost: 21.0,
        isPaid: true,
        paidAt: '2024-07-03 14:20:00'
      }
    ];

    for (const bill of utilityBills) {
      await connection.execute(`
        INSERT INTO utility_bills (
          id, student_id, room_id, month, electricity_usage, electricity_cost,
          water_usage, water_cost, total_cost, is_paid, paid_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        bill.id, bill.studentId, bill.roomId, bill.month, bill.electricityUsage,
        bill.electricityCost, bill.waterUsage, bill.waterCost, bill.totalCost,
        bill.isPaid, bill.paidAt
      ]);
    }

    // 导入违规记录数据
    console.log('⚠️ 导入违规记录数据...');
    const violations = [
      {
        id: 'vio001',
        studentId: 'student01',
        dormBuildingId: 'bldgA',
        date: '2024-07-15',
        type: '违规电器',
        description: '宿舍内使用大功率吹风机',
        actionTaken: '口头警告',
        recordedBy: 'dormadmin01'
      },
      {
        id: 'vio002',
        studentId: 'student02',
        dormBuildingId: 'bldgB',
        date: '2024-07-16',
        type: '晚归',
        description: '超过规定时间返校',
        actionTaken: '记录一次',
        recordedBy: 'dormadmin02'
      }
    ];

    for (const violation of violations) {
      await connection.execute(`
        INSERT INTO violations (id, student_id, dorm_building_id, date, type, description, action_taken, recorded_by)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        violation.id, violation.studentId, violation.dormBuildingId, violation.date,
        violation.type, violation.description, violation.actionTaken, violation.recordedBy
      ]);
    }

    // 导入晚归记录数据
    console.log('🌙 导入晚归记录数据...');
    const lateReturns = [
      {
        id: 'lr001',
        studentId: 'student01',
        dormBuildingId: 'bldgA',
        date: '2024-07-18',
        time: '23:30:00',
        reason: '图书馆学习',
        recordedBy: 'dormadmin01'
      },
      {
        id: 'lr002',
        studentId: 'student03',
        dormBuildingId: 'bldgA',
        date: '2024-07-19',
        time: '00:15:00',
        reason: '社团活动',
        recordedBy: 'dormadmin01'
      }
    ];

    for (const lateReturn of lateReturns) {
      await connection.execute(`
        INSERT INTO late_returns (id, student_id, dorm_building_id, date, time, reason, recorded_by)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        lateReturn.id, lateReturn.studentId, lateReturn.dormBuildingId, lateReturn.date,
        lateReturn.time, lateReturn.reason, lateReturn.recordedBy
      ]);
    }

    // 导入访客记录数据
    console.log('👥 导入访客记录数据...');
    const visitors = [
      {
        id: 'vis001',
        visitorName: '访客甲',
        visitorIdNumber: '123456789012345X',
        reason: '探亲',
        entryTime: '2024-07-20 14:00:00',
        exitTime: '2024-07-20 16:00:00',
        visitedStudentId: 'student01',
        dormBuildingId: 'bldgA',
        recordedBy: 'dormadmin01'
      },
      {
        id: 'vis002',
        visitorName: '访客乙',
        visitorIdNumber: '987654321098765Y',
        reason: '送东西',
        entryTime: '2024-07-21 10:00:00',
        exitTime: null,
        visitedStudentId: 'student02',
        dormBuildingId: 'bldgB',
        recordedBy: 'dormadmin02'
      }
    ];

    for (const visitor of visitors) {
      await connection.execute(`
        INSERT INTO visitors (
          id, visitor_name, visitor_id_number, reason, entry_time, exit_time,
          visited_student_id, dorm_building_id, recorded_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        visitor.id, visitor.visitorName, visitor.visitorIdNumber, visitor.reason,
        visitor.entryTime, visitor.exitTime, visitor.visitedStudentId,
        visitor.dormBuildingId, visitor.recordedBy
      ]);
    }

    // 导入文明宿舍评分数据
    console.log('🏆 导入文明宿舍评分数据...');
    const civilizedScores = [
      {
        id: 'cds001',
        dormBuildingId: 'bldgA',
        roomId: 'roomA101',
        date: '2024-07-01',
        score: 95,
        notes: '卫生良好，物品摆放整齐。',
        recordedBy: 'dormadmin01'
      },
      {
        id: 'cds002',
        dormBuildingId: 'bldgB',
        roomId: 'roomB205',
        date: '2024-07-01',
        score: 88,
        notes: '阳台有杂物，已提醒。',
        recordedBy: 'dormadmin02'
      },
      {
        id: 'cds003',
        dormBuildingId: 'bldgA',
        roomId: 'roomA102',
        date: '2024-07-01',
        score: 92,
        notes: '整体不错。',
        recordedBy: 'dormadmin01'
      }
    ];

    for (const score of civilizedScores) {
      await connection.execute(`
        INSERT INTO civilized_dorm_scores (id, dorm_building_id, room_id, date, score, notes, recorded_by)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `, [
        score.id, score.dormBuildingId, score.roomId, score.date,
        score.score, score.notes, score.recordedBy
      ]);
    }

    // 验证导入结果
    console.log('🔍 验证导入结果...');
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [collegeCount] = await connection.execute('SELECT COUNT(*) as count FROM colleges');
    const [majorCount] = await connection.execute('SELECT COUNT(*) as count FROM majors');
    const [buildingCount] = await connection.execute('SELECT COUNT(*) as count FROM dorm_buildings');
    const [roomCount] = await connection.execute('SELECT COUNT(*) as count FROM rooms');
    const [bedCount] = await connection.execute('SELECT COUNT(*) as count FROM beds');
    const [repairCount] = await connection.execute('SELECT COUNT(*) as count FROM repair_requests');
    const [announcementCount] = await connection.execute('SELECT COUNT(*) as count FROM announcements');
    const [billCount] = await connection.execute('SELECT COUNT(*) as count FROM utility_bills');
    const [violationCount] = await connection.execute('SELECT COUNT(*) as count FROM violations');
    const [lateReturnCount] = await connection.execute('SELECT COUNT(*) as count FROM late_returns');
    const [visitorCount] = await connection.execute('SELECT COUNT(*) as count FROM visitors');
    const [scoreCount] = await connection.execute('SELECT COUNT(*) as count FROM civilized_dorm_scores');

    console.log(`
🎉 所有模拟数据导入完成！

📊 数据统计:
   - 用户: ${userCount[0].count} 个
   - 学院: ${collegeCount[0].count} 个
   - 专业: ${majorCount[0].count} 个
   - 宿舍楼: ${buildingCount[0].count} 个
   - 房间: ${roomCount[0].count} 个
   - 床位: ${bedCount[0].count} 个
   - 维修请求: ${repairCount[0].count} 个
   - 公告: ${announcementCount[0].count} 个
   - 水电费账单: ${billCount[0].count} 个
   - 违规记录: ${violationCount[0].count} 个
   - 晚归记录: ${lateReturnCount[0].count} 个
   - 访客记录: ${visitorCount[0].count} 个
   - 文明宿舍评分: ${scoreCount[0].count} 个

🔐 测试账户:
   - 系统管理员: <EMAIL> / password123
   - 宿舍管理员A栋: <EMAIL> / password123
   - 宿舍管理员B栋: <EMAIL> / password123
   - 学生王五: <EMAIL> / password123
   - 学生赵六: <EMAIL> / password123
   - 学生孙七: <EMAIL> / password123
   - 维修人员: <EMAIL> / password123

🚀 现在可以使用真实数据测试前后端连接了！
    `);

  } catch (error) {
    console.error('❌ 数据导入失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

importMockData();
