
import React, { useState, FormEvent } from 'react';
import { Violation, User, UserRole } from '../../types';
import { MOCK_VIOLATIONS, MOCK_USERS, getBuildingIdByName } from '../../constants';
import { useAuth } from '../../contexts/AuthContext';
import Table from '../../components/Table';
import Card from '../../components/Card';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import Input from '../../components/Input';

const ViolationRecordPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [violations, setViolations] = useState<Violation[]>(MOCK_VIOLATIONS);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newViolation, setNewViolation] = useState<Partial<Violation>>({});
  
  if (!currentUser || currentUser.role !== UserRole.DORM_ADMIN) return <p>权限不足。</p>;

  const adminBuildingId = getBuildingIdByName(currentUser.dormBuilding);
  const buildingName = currentUser.dormBuilding || "未知楼栋";
  
  const studentsInAdminBuilding = MOCK_USERS.filter(u => u.role === UserRole.STUDENT && getBuildingIdByName(u.dormBuilding) === adminBuildingId);
  const filteredViolations = violations.filter(v => v.dormBuildingId === adminBuildingId);

  const columns = [
    { header: '学生姓名', accessor: 'studentName' as keyof Violation },
    { header: '日期', accessor: 'date' as keyof Violation },
    { header: '违规类型', accessor: 'type' as keyof Violation },
    { header: '描述', accessor: 'description' as keyof Violation, render: (v: Violation) => <span title={v.description}>{v.description.substring(0,30)}...</span> },
    { header: '处理措施', accessor: (v:Violation) => v.actionTaken || "N/A" },
    { 
      header: '操作', 
      accessor: 'id' as keyof Violation,
      render: (violation: Violation) => (
        <Button size="sm" variant="danger" onClick={() => handleDelete(violation.id)}><i className="fas fa-trash"></i></Button>
      )
    }
  ];

  const handleOpenModal = () => {
    setNewViolation({ recordedBy: currentUser.id, dormBuildingId: adminBuildingId, date: new Date().toISOString().split('T')[0] });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setNewViolation({});
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name === "studentId") {
        const student = studentsInAdminBuilding.find(s => s.id === value);
        setNewViolation(prev => ({ ...prev, studentId: value, studentName: student?.name }));
    } else {
        setNewViolation(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newViolation.studentId && newViolation.type && newViolation.description) {
      setViolations([...violations, { ...newViolation, id: `vio_${Date.now()}` } as Violation]);
      handleCloseModal();
    } else {
        alert("请填写所有必填项。");
    }
  };

  const handleDelete = (id: string) => {
    if (window.confirm("确定删除此违规记录吗?")) {
        setViolations(violations.filter(v => v.id !== id));
    }
  }

  return (
    <Card title={`${buildingName} - 违规记录管理`} actions={<Button onClick={handleOpenModal} leftIcon={<i className="fas fa-plus mr-2"></i>}>添加记录</Button>}>
      <Table columns={columns} data={filteredViolations} keyExtractor={v => v.id} />
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title="添加违规记录">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="studentId" className="block text-sm font-medium text-gray-700 mb-1">学生</label>
            <select
              id="studentId"
              name="studentId"
              value={newViolation.studentId || ''}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              required
            >
              <option value="">-- 选择学生 --</option>
              {studentsInAdminBuilding.map(s => <option key={s.id} value={s.id}>{s.name} ({s.roomNumber})</option>)}
            </select>
          </div>
          <Input name="date" label="日期" type="date" value={newViolation.date || ''} onChange={handleInputChange} required />
          <Input name="type" label="违规类型" value={newViolation.type || ''} onChange={handleInputChange} placeholder="例如：使用违禁电器、晚归" required />
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">详细描述</label>
            <textarea id="description" name="description" value={newViolation.description || ''} onChange={handleInputChange} rows={3} className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" required />
          </div>
          <Input name="actionTaken" label="处理措施 (可选)" value={newViolation.actionTaken || ''} onChange={handleInputChange} />
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseModal}>取消</Button>
            <Button type="submit">添加记录</Button>
          </div>
        </form>
      </Modal>
    </Card>
  );
};

export default ViolationRecordPage;
