
import React, { useState } from 'react';
import { DormBuilding, User, UserRole } from '../../types';
import { MOCK_DORM_BUILDINGS, MOCK_USERS } from '../../constants';
import Table from '../../components/Table';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import Input from '../../components/Input';
import Card from '../../components/Card';

const DormBuildingManagementPage: React.FC = () => {
  const [buildings, setBuildings] = useState<DormBuilding[]>(MOCK_DORM_BUILDINGS);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingBuilding, setEditingBuilding] = useState<DormBuilding | null>(null);
  const [newBuilding, setNewBuilding] = useState<Partial<DormBuilding>>({});

  const dormAdmins = MOCK_USERS.filter(u => u.role === UserRole.DORM_ADMIN);

  const columns = [
    { header: '楼栋名称', accessor: 'name' as keyof DormBuilding },
    { header: '楼层数', accessor: 'floors' as keyof DormBuilding },
    { header: '总房间数', accessor: 'totalRooms' as keyof DormBuilding },
    { header: '分配的宿管', accessor: (item: DormBuilding) => dormAdmins.find(admin => admin.id === item.assignedAdminId)?.name || '未分配' },
    { 
      header: '操作', 
      accessor: 'id' as keyof DormBuilding,
      render: (building: DormBuilding) => (
        <div className="space-x-2">
          <Button size="sm" variant="ghost" onClick={() => handleEdit(building)}><i className="fas fa-edit"></i></Button>
          <Button size="sm" variant="danger" onClick={() => handleDelete(building.id)}><i className="fas fa-trash"></i></Button>
        </div>
      )
    }
  ];

  const handleEdit = (building: DormBuilding) => {
    setEditingBuilding(building);
    setNewBuilding(building);
    setIsModalOpen(true);
  };

  const handleDelete = (buildingId: string) => {
    if (window.confirm('您确定要删除此宿舍楼吗？')) {
      setBuildings(buildings.filter(b => b.id !== buildingId));
    }
  };
  
  const handleOpenModal = (building: DormBuilding | null = null) => {
    setEditingBuilding(building);
    setNewBuilding(building ? { ...building } : { id: `bldg${Date.now()}`, name: '', floors: 0, totalRooms: 0 });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingBuilding(null);
    setNewBuilding({});
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewBuilding(prev => ({ ...prev, [name]: name === 'floors' || name === 'totalRooms' ? parseInt(value) : value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingBuilding) {
      setBuildings(buildings.map(b => b.id === editingBuilding.id ? { ...newBuilding } as DormBuilding : b));
    } else {
      setBuildings([...buildings, { ...newBuilding, id: `bldg_${Date.now()}` } as DormBuilding]);
    }
    handleCloseModal();
  };

  return (
    <Card title="宿舍楼管理" actions={<Button onClick={() => handleOpenModal()} leftIcon={<i className="fas fa-plus mr-2"></i>}>添加宿舍楼</Button>}>
      <Table columns={columns} data={buildings} keyExtractor={(b) => b.id} />

      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={editingBuilding ? '编辑宿舍楼' : '添加新宿舍楼'}>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input name="name" label="楼栋名称" value={newBuilding.name || ''} onChange={handleInputChange} required />
          <Input name="floors" label="楼层数" type="number" value={newBuilding.floors || 0} onChange={handleInputChange} required />
          <Input name="totalRooms" label="总房间数" type="number" value={newBuilding.totalRooms || 0} onChange={handleInputChange} required />
          <div>
            <label htmlFor="assignedAdminId" className="block text-sm font-medium text-gray-700 mb-1">分配宿舍管理员 (可选)</label>
            <select
              id="assignedAdminId"
              name="assignedAdminId"
              value={newBuilding.assignedAdminId || ''}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">-- 选择管理员 --</option>
              {dormAdmins.map(admin => (
                <option key={admin.id} value={admin.id}>{admin.name}</option>
              ))}
            </select>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseModal}>取消</Button>
            <Button type="submit">{editingBuilding ? '保存更改' : '添加宿舍楼'}</Button>
          </div>
        </form>
      </Modal>
    </Card>
  );
};

export default DormBuildingManagementPage;