
import React, { useState, FormEvent } from 'react';
import { LateReturn, UserRole } from '../../types';
import { MOCK_LATE_RETURNS, MOCK_USERS, getBuildingIdByName } from '../../constants';
import { useAuth } from '../../contexts/AuthContext';
import Table from '../../components/Table';
import Card from '../../components/Card';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import Input from '../../components/Input';

const LateReturnRecordPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [lateReturns, setLateReturns] = useState<LateReturn[]>(MOCK_LATE_RETURNS);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newLateReturn, setNewLateReturn] = useState<Partial<LateReturn>>({});

  if (!currentUser || currentUser.role !== UserRole.DORM_ADMIN) return <p>权限不足。</p>;

  const adminBuildingId = getBuildingIdByName(currentUser.dormBuilding);
  const buildingName = currentUser.dormBuilding || "未知楼栋";

  const studentsInAdminBuilding = MOCK_USERS.filter(u => u.role === UserRole.STUDENT && getBuildingIdByName(u.dormBuilding) === adminBuildingId);
  const filteredLateReturns = lateReturns.filter(lr => lr.dormBuildingId === adminBuildingId);

  const columns = [
    { header: '学生姓名', accessor: 'studentName' as keyof LateReturn },
    { header: '日期', accessor: 'date' as keyof LateReturn },
    { header: '时间', accessor: 'time' as keyof LateReturn },
    { header: '原因', accessor: 'reason' as keyof LateReturn },
    { 
      header: '操作', 
      accessor: 'id' as keyof LateReturn,
      render: (record: LateReturn) => (
        <Button size="sm" variant="danger" onClick={() => handleDelete(record.id)}><i className="fas fa-trash"></i></Button>
      )
    }
  ];

  const handleOpenModal = () => {
    setNewLateReturn({ recordedBy: currentUser.id, dormBuildingId: adminBuildingId, date: new Date().toISOString().split('T')[0], time: "23:00" });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setNewLateReturn({});
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
     if (name === "studentId") {
        const student = studentsInAdminBuilding.find(s => s.id === value);
        setNewLateReturn(prev => ({ ...prev, studentId: value, studentName: student?.name }));
    } else {
        setNewLateReturn(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newLateReturn.studentId && newLateReturn.date && newLateReturn.time && newLateReturn.reason) {
      setLateReturns([...lateReturns, { ...newLateReturn, id: `lr_${Date.now()}` } as LateReturn]);
      handleCloseModal();
    } else {
        alert("请填写所有必填项。");
    }
  };
  
  const handleDelete = (id: string) => {
    if (window.confirm("确定删除此晚归记录吗?")) {
        setLateReturns(lateReturns.filter(lr => lr.id !== id));
    }
  }

  return (
    <Card title={`${buildingName} - 晚归记录管理`} actions={<Button onClick={handleOpenModal} leftIcon={<i className="fas fa-user-clock mr-2"></i>}>登记晚归</Button>}>
      <Table columns={columns} data={filteredLateReturns} keyExtractor={lr => lr.id} />
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title="登记晚归">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="studentId" className="block text-sm font-medium text-gray-700 mb-1">学生</label>
            <select
              id="studentId"
              name="studentId"
              value={newLateReturn.studentId || ''}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              required
            >
              <option value="">-- 选择学生 --</option>
              {studentsInAdminBuilding.map(s => <option key={s.id} value={s.id}>{s.name} ({s.roomNumber})</option>)}
            </select>
          </div>
          <Input name="date" label="日期" type="date" value={newLateReturn.date || ''} onChange={handleInputChange} required />
          <Input name="time" label="时间" type="time" value={newLateReturn.time || ''} onChange={handleInputChange} required />
          <Input name="reason" label="原因" value={newLateReturn.reason || ''} onChange={handleInputChange} required />
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseModal}>取消</Button>
            <Button type="submit">保存记录</Button>
          </div>
        </form>
      </Modal>
    </Card>
  );
};

export default LateReturnRecordPage;
