import mysql from 'mysql2/promise';
import bcrypt from 'bcryptjs';
import dotenv from 'dotenv';

dotenv.config();

const createBasicTables = async () => {
  let connection;
  
  try {
    console.log('🔄 连接到MySQL数据库...');
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'redhat',
      charset: 'utf8mb4'
    });

    console.log('✅ 数据库连接成功');

    // 创建学院表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS colleges (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ 学院表创建成功');

    // 创建专业表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS majors (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        college_id VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE CASCADE,
        UNIQUE KEY unique_major_college (name, college_id)
      )
    `);
    console.log('✅ 专业表创建成功');

    // 创建宿舍楼表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dorm_buildings (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        floors INT NOT NULL,
        total_rooms INT NOT NULL,
        assigned_admin_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ 宿舍楼表创建成功');

    // 创建用户表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(255) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        role ENUM('系统管理员', '宿舍管理员', '学生', '维修人员') NOT NULL,
        college_id VARCHAR(50),
        major_id VARCHAR(50),
        dorm_building_id VARCHAR(50),
        room_number VARCHAR(20),
        emergency_contact_name VARCHAR(100),
        emergency_contact_phone VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE SET NULL,
        FOREIGN KEY (major_id) REFERENCES majors(id) ON DELETE SET NULL,
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE SET NULL
      )
    `);
    console.log('✅ 用户表创建成功');

    // 创建房间表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS rooms (
        id VARCHAR(50) PRIMARY KEY,
        room_number VARCHAR(20) NOT NULL,
        dorm_building_id VARCHAR(50) NOT NULL,
        floor INT NOT NULL,
        type ENUM('单人间', '双人间', '四人间', '六人间') NOT NULL,
        capacity INT NOT NULL,
        occupied_beds INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
        UNIQUE KEY unique_room_building (room_number, dorm_building_id)
      )
    `);
    console.log('✅ 房间表创建成功');

    // 创建维修请求表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS repair_requests (
        id VARCHAR(50) PRIMARY KEY,
        student_id VARCHAR(50) NOT NULL,
        room_id VARCHAR(50) NOT NULL,
        title VARCHAR(200) NOT NULL,
        description TEXT NOT NULL,
        status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消') DEFAULT '待处理',
        assigned_staff_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        completed_at TIMESTAMP NULL,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
        FOREIGN KEY (assigned_staff_id) REFERENCES users(id) ON DELETE SET NULL
      )
    `);
    console.log('✅ 维修请求表创建成功');

    // 插入初始数据
    console.log('🔄 插入初始数据...');

    // 插入学院数据
    await connection.execute(`
      INSERT IGNORE INTO colleges (id, name) VALUES
      ('college001', '工程学院'),
      ('college002', '文学院'),
      ('college003', '理学院'),
      ('college004', '商学院')
    `);

    // 插入专业数据
    await connection.execute(`
      INSERT IGNORE INTO majors (id, name, college_id) VALUES
      ('major001', '计算机科学', 'college001'),
      ('major002', '软件工程', 'college001'),
      ('major003', '电子工程', 'college001'),
      ('major004', '中文文学', 'college002'),
      ('major005', '英语', 'college002'),
      ('major006', '数学', 'college003'),
      ('major007', '物理学', 'college003'),
      ('major008', '工商管理', 'college004'),
      ('major009', '会计学', 'college004')
    `);

    // 插入宿舍楼数据
    await connection.execute(`
      INSERT IGNORE INTO dorm_buildings (id, name, floors, total_rooms) VALUES
      ('bldgA', 'A栋 (阿尔法楼)', 5, 100),
      ('bldgB', 'B栋 (贝塔公寓)', 6, 120),
      ('bldgC', 'C栋 (伽马宿舍)', 4, 80)
    `);

    // 插入用户数据
    const hashedPassword = await bcrypt.hash('password123', 12);

    await connection.execute(`
      INSERT IGNORE INTO users (id, name, email, password, role) VALUES
      ('sysadmin01', '系统管理员用户', '<EMAIL>', ?, '系统管理员')
    `, [hashedPassword]);

    await connection.execute(`
      INSERT IGNORE INTO users (id, name, email, password, role, dorm_building_id) VALUES
      ('dormadmin01', '张三 (A栋宿舍管理员)', '<EMAIL>', ?, '宿舍管理员', 'bldgA'),
      ('dormadmin02', '李四 (B栋宿舍管理员)', '<EMAIL>', ?, '宿舍管理员', 'bldgB')
    `, [hashedPassword, hashedPassword]);

    await connection.execute(`
      INSERT IGNORE INTO users (id, name, email, password, phone, role, college_id, major_id, dorm_building_id, room_number, emergency_contact_name, emergency_contact_phone) VALUES
      ('student01', '王五', '<EMAIL>', ?, '13000000001', '学生', 'college001', 'major001', 'bldgA', '101', '王大锤', '13800138000'),
      ('student02', '赵六', '<EMAIL>', ?, '13000000002', '学生', 'college002', 'major004', 'bldgB', '205', '赵小花', '13800138001'),
      ('student03', '孙七', '<EMAIL>', ?, '13000000003', '学生', 'college001', 'major002', 'bldgA', '102', '孙大明', '13800138002')
    `, [hashedPassword, hashedPassword, hashedPassword]);

    await connection.execute(`
      INSERT IGNORE INTO users (id, name, email, password, role) VALUES
      ('repair01', '维修师傅', '<EMAIL>', ?, '维修人员')
    `, [hashedPassword]);

    // 更新宿舍楼管理员分配
    await connection.execute(`UPDATE dorm_buildings SET assigned_admin_id = 'dormadmin01' WHERE id = 'bldgA'`);
    await connection.execute(`UPDATE dorm_buildings SET assigned_admin_id = 'dormadmin02' WHERE id = 'bldgB'`);

    // 插入房间数据
    await connection.execute(`
      INSERT IGNORE INTO rooms (id, room_number, dorm_building_id, floor, type, capacity, occupied_beds) VALUES
      ('roomA101', '101', 'bldgA', 1, '六人间', 6, 1),
      ('roomA102', '102', 'bldgA', 1, '双人间', 2, 1),
      ('roomB205', '205', 'bldgB', 2, '六人间', 6, 1),
      ('roomC301', '301', 'bldgC', 3, '单人间', 1, 0)
    `);

    console.log('✅ 初始数据插入成功');

    // 验证数据
    const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [colleges] = await connection.execute('SELECT COUNT(*) as count FROM colleges');
    const [majors] = await connection.execute('SELECT COUNT(*) as count FROM majors');

    console.log(`
🎉 数据库初始化完成！

📊 数据统计:
   - 用户: ${users[0].count} 个
   - 学院: ${colleges[0].count} 个
   - 专业: ${majors[0].count} 个

🔐 测试账户:
   - 系统管理员: <EMAIL> / password123
   - 宿舍管理员: <EMAIL> / password123
   - 学生: <EMAIL> / password123
   - 维修人员: <EMAIL> / password123

🚀 现在可以测试前后端连接了！
    `);

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

createBasicTables();
