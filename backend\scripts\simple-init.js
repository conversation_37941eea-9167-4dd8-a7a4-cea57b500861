import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const testConnection = async () => {
  try {
    console.log('🔄 测试MySQL连接...');
    
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root'
    });

    console.log('✅ MySQL连接成功');
    
    // 创建数据库
    await connection.execute('CREATE DATABASE IF NOT EXISTS redhat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    console.log('✅ 数据库 redhat 创建成功');
    
    await connection.end();
    console.log('✅ 连接已关闭');
    
  } catch (error) {
    console.error('❌ 连接失败:', error.message);
    console.log('请确保MySQL服务正在运行，并且用户名密码正确');
  }
};

testConnection();
