
import React, { useState, FormEvent } from 'react';
import { User, Room, Bed, BedStatus, UserRole } from '../../types';
import { MOCK_USERS, MOCK_ROOMS, MOCK_BEDS, MOCK_DORM_BUILDINGS, getBuildingIdByName } from '../../constants';
import { useAuth } from '../../contexts/AuthContext';
import Table from '../../components/Table';
import Card from '../../components/Card';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import Input from '../../components/Input';

const StudentAllocationPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [users, setUsers] = useState<User[]>(MOCK_USERS);
  const [beds, setBeds] = useState<Bed[]>(MOCK_BEDS);
  
  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [isEditInfoModalOpen, setIsEditInfoModalOpen] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<User | null>(null);
  const [studentToEdit, setStudentToEdit] = useState<Partial<User>>({});
  const [selectedBedId, setSelectedBedId] = useState<string>('');

  if (!currentUser || currentUser.role !== UserRole.DORM_ADMIN) {
    return <p>权限不足。</p>;
  }
  
  const adminBuildingId = getBuildingIdByName(currentUser.dormBuilding);
  const buildingName = currentUser.dormBuilding || "未知楼栋";

  const studentsInBuilding = users.filter(user => user.role === UserRole.STUDENT && getBuildingIdByName(user.dormBuilding) === adminBuildingId);
  const roomsInBuilding = MOCK_ROOMS.filter(room => room.dormBuildingId === adminBuildingId);
  const vacantBedsInBuilding = beds.filter(bed => {
    const room = roomsInBuilding.find(r => r.id === bed.roomId);
    return room && bed.status === BedStatus.VACANT;
  });

  const studentColumns = [
    { header: '姓名', accessor: 'name' as keyof User },
    { header: '邮箱', accessor: 'email' as keyof User },
    { header: '学生电话', accessor: (user: User) => user.phone || '未填写' },
    { header: '当前房间', accessor: (user: User) => user.roomNumber || '未分配' },
    { header: '当前床位', accessor: (user: User) => beds.find(b => b.studentId === user.id)?.bedNumber || 'N/A' },
    { 
      header: '第一紧急联系人信息', 
      accessor: (user: User) => {
        if (user.emergencyContactName && user.emergencyContactPhone) {
          return `${user.emergencyContactName} (${user.emergencyContactPhone})`;
        }
        if (user.emergencyContactName) {
          return user.emergencyContactName;
        }
        if (user.emergencyContactPhone) {
          return user.emergencyContactPhone;
        }
        return '未填写';
      } 
    },
    {
      header: '操作',
      accessor: 'id' as keyof User,
      render: (user: User) => (
        <div className="space-x-2 whitespace-nowrap">
            <Button size="sm" variant="ghost" onClick={() => handleOpenAssignModal(user)}>
                {user.roomNumber ? '调整床位' : '分配床位'}
            </Button>
            <Button size="sm" variant="ghost" onClick={() => handleOpenEditInfoModal(user)}>
                <i className="fas fa-user-edit mr-1"></i> 编辑信息
            </Button>
            {user.roomNumber && (
                <Button size="sm" variant="danger" onClick={() => handleUnassignStudent(user.id)}>
                    取消分配
                </Button>
            )}
        </div>
      ),
    },
  ];
  
  const handleOpenAssignModal = (student: User) => {
    setSelectedStudent(student);
    setSelectedBedId('');
    setIsAssignModalOpen(true);
  };

  const handleCloseAssignModal = () => {
    setIsAssignModalOpen(false);
    setSelectedStudent(null);
    setSelectedBedId('');
  };
  
  const handleOpenEditInfoModal = (student: User) => {
    setSelectedStudent(student);
    setStudentToEdit({ ...student });
    setIsEditInfoModalOpen(true);
  };

  const handleCloseEditInfoModal = () => {
    setIsEditInfoModalOpen(false);
    setSelectedStudent(null);
    setStudentToEdit({});
  };
  
  const handleStudentInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setStudentToEdit(prev => ({ ...prev, [name]: value }));
  };

  const handleSaveStudentInfo = (e: FormEvent) => {
    e.preventDefault();
    if (selectedStudent) {
        // Only update emergency contact details for now from dorm admin
        setUsers(prevUsers => prevUsers.map(u => 
            u.id === selectedStudent.id ? 
            {...u, 
                emergencyContactName: studentToEdit.emergencyContactName, 
                emergencyContactPhone: studentToEdit.emergencyContactPhone 
            } : u));
        handleCloseEditInfoModal();
    }
  };


  const handleAssignStudent = () => {
    if (selectedStudent && selectedBedId) {
      const bedToAssign = beds.find(b => b.id === selectedBedId);
      const roomOfBed = roomsInBuilding.find(r => r.id === bedToAssign?.roomId);

      if (!bedToAssign || !roomOfBed) {
        alert("选择的床位或房间无效。");
        return;
      }

      // Update student
      const updatedUsers = users.map(u => {
        if (u.id === selectedStudent.id) {
          return { ...u, roomNumber: roomOfBed.roomNumber, dormBuilding: currentUser.dormBuilding };
        }
        return u;
      });
      setUsers(updatedUsers);

      // Update beds: vacate old bed (if any), occupy new bed
      const updatedBeds = beds.map(b => {
        if (b.studentId === selectedStudent.id) { // Vacate old bed
          return { ...b, status: BedStatus.VACANT, studentId: undefined };
        }
        if (b.id === selectedBedId) { // Occupy new bed
          return { ...b, status: BedStatus.OCCUPIED, studentId: selectedStudent.id };
        }
        return b;
      });
      setBeds(updatedBeds);
      
      handleCloseAssignModal();
    }
  };
  
  const handleUnassignStudent = (studentId: string) => {
    if (!window.confirm("确定要取消该学生的床位分配吗？")) return;

    setUsers(prev => prev.map(u => u.id === studentId ? {...u, roomNumber: undefined, dormBuilding: undefined} : u));
    setBeds(prev => prev.map(b => b.studentId === studentId ? {...b, status: BedStatus.VACANT, studentId: undefined} : b));
  };


  return (
    <div className="space-y-6">
      <Card title={`${buildingName} - 学生分配管理`}>
        <h3 className="text-xl font-semibold mb-2">学生列表</h3>
        <Table columns={studentColumns} data={studentsInBuilding} keyExtractor={user => user.id} emptyStateMessage="此楼栋暂无学生记录。"/>
      </Card>
      
      <Card title={`${buildingName} - 可用床位`}>
          {vacantBedsInBuilding.length === 0 ? <p>暂无可用床位。</p> : (
            <ul>
                {vacantBedsInBuilding.map(bed => {
                    const room = roomsInBuilding.find(r => r.id === bed.roomId);
                    return (
                        <li key={bed.id} className="py-1">房间 {room?.roomNumber} - 床位 {bed.bedNumber}</li>
                    );
                })}
            </ul>
          )}
      </Card>

      {/* Assign/Adjust Bed Modal */}
      {selectedStudent && (
        <Modal isOpen={isAssignModalOpen} onClose={handleCloseAssignModal} title={`为 ${selectedStudent.name} 分配/调整床位`}>
          <div className="space-y-4">
            <p>选择一个空闲床位进行分配：</p>
            <select
              value={selectedBedId}
              onChange={(e) => setSelectedBedId(e.target.value)}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">-- 请选择床位 --</option>
              {vacantBedsInBuilding.map(bed => {
                const room = roomsInBuilding.find(r => r.id === bed.roomId);
                return <option key={bed.id} value={bed.id}>房间 {room?.roomNumber} - 床位 {bed.bedNumber}</option>;
              })}
              {vacantBedsInBuilding.length === 0 && <option disabled>此楼栋暂无可分配床位</option>}
            </select>
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="ghost" onClick={handleCloseAssignModal}>取消</Button>
              <Button onClick={handleAssignStudent} disabled={!selectedBedId}>确认分配</Button>
            </div>
          </div>
        </Modal>
      )}

      {/* Edit Student Info Modal */}
      {selectedStudent && isEditInfoModalOpen && (
        <Modal isOpen={isEditInfoModalOpen} onClose={handleCloseEditInfoModal} title={`编辑 ${selectedStudent.name} 的信息`}>
            <form onSubmit={handleSaveStudentInfo} className="space-y-4">
                <Input label="姓名" value={studentToEdit.name || ''} readOnly disabled containerClassName="bg-gray-100 p-2 rounded"/>
                <Input label="邮箱" value={studentToEdit.email || ''} readOnly disabled containerClassName="bg-gray-100 p-2 rounded"/>
                <Input label="学生电话" value={studentToEdit.phone || '未填写'} readOnly disabled containerClassName="bg-gray-100 p-2 rounded"/>
                <Input 
                    name="emergencyContactName" 
                    label="第一紧急联系人姓名" 
                    value={studentToEdit.emergencyContactName || ''} 
                    onChange={handleStudentInfoChange} 
                />
                <Input 
                    name="emergencyContactPhone" 
                    label="第一紧急联系人电话" 
                    type="tel" 
                    value={studentToEdit.emergencyContactPhone || ''} 
                    onChange={handleStudentInfoChange}
                    placeholder="例如: 13800138000" 
                />
                 <div className="flex justify-end space-x-2 pt-4">
                    <Button type="button" variant="ghost" onClick={handleCloseEditInfoModal}>取消</Button>
                    <Button type="submit">保存更改</Button>
                </div>
            </form>
        </Modal>
      )}
    </div>
  );
};

export default StudentAllocationPage;