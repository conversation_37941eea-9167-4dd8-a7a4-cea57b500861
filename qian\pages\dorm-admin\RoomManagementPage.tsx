import React, { useState } from 'react';
import { Room, Bed, BedStatus, RoomType } from '../../types';
import { MOCK_ROOMS, MOCK_BEDS, MOCK_DORM_BUILDINGS, getBuildingIdByName } from '../../constants';
import { useAuth } from '../../contexts/AuthContext';
import Table from '../../components/Table';
import Card from '../../components/Card';
import Modal from '../../components/Modal';
import Button from '../../components/Button';
import Input from '../../components/Input';

const getDefaultCapacity = (type: RoomType): number => {
    switch (type) {
        case RoomType.SINGLE: return 1;
        case RoomType.DOUBLE: return 2;
        case RoomType.QUAD: return 4;
        case RoomType.HEXA: return 6;
        default: return 4;
    }
};

const RoomManagementPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [rooms, setRooms] = useState<Room[]>(MOCK_ROOMS);
  const [beds, setBeds] = useState<Bed[]>(MOCK_BEDS); 

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [viewingRoom, setViewingRoom] = useState<Room | null>(null);
  
  const [isAddRoomModalOpen, setIsAddRoomModalOpen] = useState(false);
  const [newRoom, setNewRoom] = useState<Partial<Room>>({ type: RoomType.QUAD, capacity: getDefaultCapacity(RoomType.QUAD), occupiedBeds: 0 });


  if (!currentUser || currentUser.role !== '宿舍管理员') {
    return <p>权限不足。</p>;
  }

  const adminBuildingId = getBuildingIdByName(currentUser.dormBuilding);
  const buildingName = currentUser.dormBuilding || "未知楼栋";

  const filteredRooms = rooms.filter(room => room.dormBuildingId === adminBuildingId);

  const roomColumns = [
    { header: '房间号', accessor: 'roomNumber' as keyof Room },
    { header: '楼层', accessor: 'floor' as keyof Room },
    { header: '类型', accessor: 'type' as keyof Room },
    { header: '容量', accessor: 'capacity' as keyof Room },
    { header: '已住人数', accessor: 'occupiedBeds' as keyof Room },
    {
      header: '操作',
      accessor: 'id' as keyof Room,
      render: (room: Room) => (
        <Button size="sm" variant="ghost" onClick={() => handleViewRoomDetails(room)}>
          查看床位
        </Button>
      ),
    },
  ];

  const bedColumns = [
    { header: '床位号', accessor: 'bedNumber' as keyof Bed },
    { header: '状态', accessor: 'status' as keyof Bed},
    { header: '学生ID', accessor: (bed: Bed) => bed.studentId || 'N/A' },
  ];
  
  const handleOpenAddRoomModal = () => {
    const defaultType = RoomType.QUAD;
    setNewRoom({ 
        dormBuildingId: adminBuildingId, 
        type: defaultType, 
        capacity: getDefaultCapacity(defaultType), 
        occupiedBeds: 0,
        floor: 1, // Default floor
    });
    setIsAddRoomModalOpen(true);
  };

  const handleCloseAddRoomModal = () => {
    setIsAddRoomModalOpen(false);
    const defaultType = RoomType.QUAD;
    setNewRoom({ type: defaultType, capacity: getDefaultCapacity(defaultType), occupiedBeds: 0 });
  };

  const handleAddRoomInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name === "type") {
        const roomType = value as RoomType;
        setNewRoom(prev => ({ ...prev, type: roomType, capacity: getDefaultCapacity(roomType) }));
    } else {
        setNewRoom(prev => ({ ...prev, [name]: name === 'floor' || name === 'capacity' || name === 'occupiedBeds' ? parseInt(value) : value }));
    }
  };

  const handleAddRoomSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newRoom.roomNumber && newRoom.floor && newRoom.capacity && adminBuildingId) {
      const roomToAdd: Room = {
        id: `room_${Date.now()}`,
        roomNumber: newRoom.roomNumber,
        dormBuildingId: adminBuildingId,
        floor: newRoom.floor!,
        type: newRoom.type || RoomType.QUAD,
        capacity: newRoom.capacity!,
        occupiedBeds: newRoom.occupiedBeds || 0,
      };
      setRooms(prev => [...prev, roomToAdd]);
      
      const newBedsForRoom: Bed[] = [];
      for (let i = 1; i <= roomToAdd.capacity; i++) {
        newBedsForRoom.push({
          id: `bed_${roomToAdd.id}_${i}`,
          roomId: roomToAdd.id,
          bedNumber: `${i}`,
          status: BedStatus.VACANT,
        });
      }
      setBeds(prev => [...prev, ...newBedsForRoom]);

      handleCloseAddRoomModal();
    }
  };


  const handleViewRoomDetails = (room: Room) => {
    setViewingRoom(room);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setViewingRoom(null);
  };

  const bedsInViewingRoom = viewingRoom ? beds.filter(bed => bed.roomId === viewingRoom.id) : [];

  return (
    <Card title={`${buildingName} - 房间管理`} actions={
        <Button onClick={handleOpenAddRoomModal} leftIcon={<i className="fas fa-plus mr-2"></i>}>添加房间</Button>
    }>
      <Table columns={roomColumns} data={filteredRooms} keyExtractor={room => room.id} />

      {viewingRoom && (
        <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={`房间 ${viewingRoom.roomNumber} - 床位详情`} size="lg">
          <Table columns={bedColumns} data={bedsInViewingRoom} keyExtractor={bed => bed.id} emptyStateMessage="此房间暂无床位信息。" />
          <div className="mt-4 flex justify-end">
            <Button onClick={handleCloseModal}>关闭</Button>
          </div>
        </Modal>
      )}
      
      <Modal isOpen={isAddRoomModalOpen} onClose={handleCloseAddRoomModal} title={`为 ${buildingName} 添加新房间`}>
        <form onSubmit={handleAddRoomSubmit} className="space-y-4">
          <Input name="roomNumber" label="房间号" value={newRoom.roomNumber || ''} onChange={handleAddRoomInputChange} required />
          <Input name="floor" type="number" label="楼层" value={newRoom.floor || ''} onChange={handleAddRoomInputChange} required min="1" />
          <div>
            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1">房间类型</label>
            <select
              id="type"
              name="type"
              value={newRoom.type}
              onChange={handleAddRoomInputChange}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              required
            >
              {Object.values(RoomType).map(type => <option key={type} value={type}>{type}</option>)}
            </select>
          </div>
          <Input name="capacity" type="number" label="容量" value={newRoom.capacity || ''} onChange={handleAddRoomInputChange} required min="1" />
           <Input name="occupiedBeds" type="number" label="已住人数 (初始)" value={newRoom.occupiedBeds || 0} onChange={handleAddRoomInputChange} required min="0" />


          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseAddRoomModal}>取消</Button>
            <Button type="submit">添加房间</Button>
          </div>
        </form>
      </Modal>
    </Card>
  );
};

export default RoomManagementPage;