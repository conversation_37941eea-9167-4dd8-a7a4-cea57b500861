import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// 获取当前文件的目录
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 加载环境变量
dotenv.config();

const resetDatabase = async () => {
  let connection;
  
  try {
    console.log('🔄 开始重置数据库...');
    
    // 连接到MySQL服务器（不指定数据库）
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      charset: 'utf8mb4',
      timezone: '+08:00'
    });

    console.log('✅ 成功连接到MySQL服务器');

    // 删除并重新创建数据库
    console.log('🗑️ 删除现有数据库...');
    await connection.execute('DROP DATABASE IF EXISTS redhat');
    
    console.log('🆕 创建新数据库...');
    await connection.execute('CREATE DATABASE redhat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    
    console.log('🔄 切换到新数据库...');
    await connection.execute('USE redhat');

    // 读取SQL文件
    const sqlFilePath = path.join(__dirname, '../../database/redhat.sql');
    if (!fs.existsSync(sqlFilePath)) {
      throw new Error(`SQL文件不存在: ${sqlFilePath}`);
    }

    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    console.log('✅ 成功读取SQL文件');

    // 分割SQL语句
    const statements = sqlContent
      .split('\n')
      .filter(line => line.trim() && !line.trim().startsWith('--'))
      .join('\n')
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.toLowerCase().includes('create database') && !stmt.toLowerCase().includes('use redhat'));

    console.log(`📝 找到 ${statements.length} 条SQL语句`);

    // 执行SQL语句
    let successCount = 0;
    let skipCount = 0;
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        try {
          await connection.execute(statement);
          successCount++;
          
          if (statement.toLowerCase().includes('create table')) {
            const tableName = statement.match(/create table\s+(?:if not exists\s+)?(\w+)/i)?.[1];
            console.log(`✅ 表 ${tableName} 创建成功`);
          } else if (statement.toLowerCase().includes('insert into')) {
            const tableName = statement.match(/insert into\s+(\w+)/i)?.[1];
            if (tableName) {
              console.log(`✅ 数据插入到 ${tableName} 表成功`);
            }
          }
        } catch (error) {
          if (error.code === 'ER_TABLE_EXISTS_ERROR') {
            console.log(`ℹ️  表已存在，跳过创建`);
            skipCount++;
          } else if (error.code === 'ER_DUP_ENTRY') {
            console.log(`ℹ️  数据已存在，跳过插入`);
            skipCount++;
          } else {
            console.error(`❌ 执行SQL语句失败: ${statement.substring(0, 50)}...`);
            console.error('错误详情:', error.message);
          }
        }
      }
    }

    console.log(`✅ SQL执行完成: ${successCount} 成功, ${skipCount} 跳过`);

    // 验证数据库重置结果
    console.log('🔍 验证数据库重置结果...');
    
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`✅ 数据库中共有 ${tables.length} 个表:`);
    tables.forEach(table => {
      const tableName = Object.values(table)[0];
      console.log(`   - ${tableName}`);
    });

    // 检查各表数据
    const tableChecks = [
      { name: 'users', desc: '用户' },
      { name: 'colleges', desc: '学院' },
      { name: 'majors', desc: '专业' },
      { name: 'dorm_buildings', desc: '宿舍楼' },
      { name: 'rooms', desc: '房间' },
      { name: 'beds', desc: '床位' },
      { name: 'repair_requests', desc: '维修请求' },
      { name: 'announcements', desc: '公告' },
      { name: 'utility_bills', desc: '水电费账单' },
      { name: 'violations', desc: '违规记录' },
      { name: 'late_returns', desc: '晚归记录' },
      { name: 'visitors', desc: '访客记录' },
      { name: 'civilized_dorm_scores', desc: '文明宿舍评分' }
    ];

    for (const tableCheck of tableChecks) {
      try {
        const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${tableCheck.name}`);
        console.log(`✅ ${tableCheck.desc}表中共有 ${rows[0].count} 条记录`);
      } catch (error) {
        console.log(`⚠️  ${tableCheck.desc}表不存在或查询失败`);
      }
    }

    console.log(`
🎉 数据库重置完成！

📊 数据库信息:
   - 数据库名: redhat
   - 主机: ${process.env.DB_HOST || 'localhost'}
   - 端口: ${process.env.DB_PORT || 3306}
   - 用户: ${process.env.DB_USER || 'root'}

🔐 测试账户:
   - 系统管理员: <EMAIL> / password123
   - 宿舍管理员A栋: <EMAIL> / password123
   - 宿舍管理员B栋: <EMAIL> / password123
   - 学生王五: <EMAIL> / password123
   - 学生赵六: <EMAIL> / password123
   - 学生孙七: <EMAIL> / password123
   - 维修人员: <EMAIL> / password123

🚀 数据库已与redhat.sql文件完全同步！
    `);

  } catch (error) {
    console.error('❌ 数据库重置失败:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

// 运行重置
resetDatabase();
