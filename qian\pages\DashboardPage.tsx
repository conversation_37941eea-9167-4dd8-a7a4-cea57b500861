import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { UserRole, RepairRequest, Announcement, UtilityBill, RepairStatus } from '../types';
import { MOCK_REPAIR_REQUESTS, MOCK_ANNOUNCEMENTS, MOCK_UTILITY_BILLS, MOCK_ROOMS, MOCK_USERS as ALL_MOCK_USERS, MOCK_DORM_BUILDINGS as ALL_MOCK_DORM_BUILDINGS, MOCK_COLLEGES as ALL_MOCK_COLLEGES } from '../constants';
import Card from '../components/Card';
import { Link } from 'react-router-dom';
import Button from '../components/Button';

const DashboardPage: React.FC = () => {
  const { currentUser } = useAuth();

  if (!currentUser) return (
    <div className="flex items-center justify-center h-64">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    </div>
  );

  const getRoleSpecificStats = () => {
    switch (currentUser.role) {
      case UserRole.SYSTEM_ADMIN:
        return [
          { 
            title: '总用户数', 
            value: ALL_MOCK_USERS.length, 
            icon: 'fa-users', 
            link: '/admin/users',
            color: 'from-blue-500 to-blue-600',
            bgColor: 'bg-blue-50'
          },
          { 
            title: '宿舍楼数', 
            value: ALL_MOCK_DORM_BUILDINGS.length, 
            icon: 'fa-building', 
            link: '/admin/dorm-buildings',
            color: 'from-green-500 to-green-600',
            bgColor: 'bg-green-50'
          },
          { 
            title: '学院数', 
            value: ALL_MOCK_COLLEGES.length, 
            icon: 'fa-landmark', 
            link: '/admin/colleges',
            color: 'from-purple-500 to-purple-600',
            bgColor: 'bg-purple-50'
          },
        ];
      case UserRole.DORM_ADMIN:
        const adminBuildingId = ALL_MOCK_DORM_BUILDINGS.find(b => b.assignedAdminId === currentUser.id)?.id;
        const roomsInBuilding = MOCK_ROOMS.filter(r => r.dormBuildingId === adminBuildingId).length;
        const pendingRepairs = MOCK_REPAIR_REQUESTS.filter(r => r.dormBuilding === currentUser.dormBuilding && r.status === RepairStatus.PENDING).length;
        return [
          { 
            title: '管理房间数', 
            value: roomsInBuilding, 
            icon: 'fa-door-open', 
            link: '/dorm-admin/rooms',
            color: 'from-indigo-500 to-indigo-600',
            bgColor: 'bg-indigo-50'
          },
          { 
            title: '待处理报修', 
            value: pendingRepairs, 
            icon: 'fa-wrench', 
            link: '/repairs',
            color: 'from-orange-500 to-orange-600',
            bgColor: 'bg-orange-50'
          },
          { 
            title: '现有公告', 
            value: MOCK_ANNOUNCEMENTS.length, 
            icon: 'fa-bullhorn', 
            link: '/announcements',
            color: 'from-pink-500 to-pink-600',
            bgColor: 'bg-pink-50'
          },
        ];
      case UserRole.STUDENT:
        const myPendingRepairs = MOCK_REPAIR_REQUESTS.filter(r => r.studentId === currentUser.id && r.status !== RepairStatus.COMPLETED && r.status !== RepairStatus.CONFIRMED).length;
        const myUnpaidBills = MOCK_UTILITY_BILLS.filter(b => b.studentId === currentUser.id && !b.isPaid).length;
        return [
          { 
            title: '我的房间', 
            value: `${currentUser.dormBuilding} - ${currentUser.roomNumber}`, 
            icon: 'fa-person-booth', 
            link: '/student/my-info',
            color: 'from-blue-500 to-blue-600',
            bgColor: 'bg-blue-50'
          },
          { 
            title: '待处理报修', 
            value: myPendingRepairs, 
            icon: 'fa-tools', 
            link: '/repairs',
            color: 'from-red-500 to-red-600',
            bgColor: 'bg-red-50'
          },
          { 
            title: '未缴水电费', 
            value: myUnpaidBills, 
            icon: 'fa-file-invoice-dollar', 
            link: '/student/utility-bills',
            color: 'from-yellow-500 to-yellow-600',
            bgColor: 'bg-yellow-50'
          },
        ];
      case UserRole.REPAIR_STAFF:
        const assignedTasks = MOCK_REPAIR_REQUESTS.filter(r => r.assignedTo === currentUser.id && (r.status === RepairStatus.ASSIGNED || r.status === RepairStatus.IN_PROGRESS)).length;
        return [
          { 
            title: '已指派任务', 
            value: assignedTasks, 
            icon: 'fa-clipboard-list', 
            link: '/repair/my-tasks',
            color: 'from-green-500 to-green-600',
            bgColor: 'bg-green-50'
          },
          { 
            title: '所有待修', 
            value: MOCK_REPAIR_REQUESTS.filter(r => r.status !== RepairStatus.COMPLETED && r.status !== RepairStatus.CONFIRMED).length, 
            icon: 'fa-tools', 
            link: '/repairs',
            color: 'from-orange-500 to-orange-600',
            bgColor: 'bg-orange-50'
          },
        ];
      default:
        return [];
    }
  };

  const stats = getRoleSpecificStats();

  return (
    <div className="space-y-8 animate-fade-in">
      {/* 欢迎横幅 */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 rounded-2xl p-8 text-white shadow-strong">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">欢迎回来, {currentUser.name}!</h1>
            <p className="text-blue-100 text-lg">今天是美好的一天，让我们开始工作吧</p>
            <p className="text-blue-200 text-sm mt-2">{new Date().toLocaleDateString('zh-CN', { 
              year: 'numeric', 
              month: 'long', 
              day: 'numeric',
              weekday: 'long'
            })}</p>
          </div>
          <div className="hidden md:block">
            <div className="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center backdrop-blur-sm">
              <i className="fas fa-chart-line text-4xl"></i>
            </div>
          </div>
        </div>
      </div>
      
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {stats.map((stat, index) => (
          <Card 
            key={stat.title} 
            className={`hover-lift ${stat.bgColor} border-0`}
            hover={true}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center mr-4 shadow-lg`}>
                  <i className={`fas ${stat.icon} text-white text-xl`}></i>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-800">{stat.title}</h3>
                  <p className={`text-2xl font-bold bg-gradient-to-r ${stat.color} bg-clip-text text-transparent`}>
                    {stat.value}
                  </p>
                </div>
              </div>
            </div>
            {stat.link && (
              <div className="mt-4 flex justify-end">
                <Link to={stat.link}>
                  <Button 
                    size="sm" 
                    variant="ghost" 
                    className="hover:bg-white hover:bg-opacity-50"
                  >
                    查看详情 <i className="fas fa-arrow-right ml-2"></i>
                  </Button>
                </Link>
              </div>
            )}
          </Card>
        ))}
      </div>

      {/* 内容区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 最新公告 */}
        <Card title="📢 最新公告" hover={true}>
          <div className="space-y-4">
            {MOCK_ANNOUNCEMENTS.slice(0, 3).map((ann, index) => (
              <div key={ann.id} className={`p-4 rounded-xl ${index % 2 === 0 ? 'bg-blue-50' : 'bg-purple-50'} hover:shadow-md transition-shadow duration-200`}>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-800 mb-1">{ann.title}</h4>
                    <p className="text-sm text-gray-600 line-clamp-2">{ann.content}</p>
                    <div className="flex items-center mt-2 text-xs text-gray-500">
                      <i className="fas fa-user mr-1"></i>
                      <span>{ann.authorName}</span>
                      <i className="fas fa-clock ml-3 mr-1"></i>
                      <span>{new Date(ann.createdAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                  <div className="ml-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  </div>
                </div>
              </div>
            ))}
            {MOCK_ANNOUNCEMENTS.length > 3 && (
              <div className="mt-4 text-center">
                <Link to="/announcements">
                  <Button variant="gradient" size="sm" rounded="full">
                    查看全部公告 <i className="fas fa-arrow-right ml-2"></i>
                  </Button>
                </Link>
              </div>
            )}
            {MOCK_ANNOUNCEMENTS.length === 0 && (
              <div className="text-center py-8">
                <i className="fas fa-inbox text-4xl text-gray-300 mb-3"></i>
                <p className="text-gray-500">暂无公告</p>
              </div>
            )}
          </div>
        </Card>

        {/* 待处理报修 */}
        {(currentUser.role === UserRole.STUDENT || currentUser.role === UserRole.DORM_ADMIN || currentUser.role === UserRole.REPAIR_STAFF) && (
          <Card title="🔧 待处理报修" hover={true}>
            <div className="space-y-4">
              {MOCK_REPAIR_REQUESTS.filter(r => r.status !== RepairStatus.COMPLETED && r.status !== RepairStatus.CONFIRMED).slice(0, 3).map((req, index) => (
                <div key={req.id} className={`p-4 rounded-xl ${index % 2 === 0 ? 'bg-orange-50' : 'bg-red-50'} hover:shadow-md transition-shadow duration-200`}>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-800 mb-1 line-clamp-1">
                        {req.description.substring(0, 50)}...
                      </h4>
                      <div className="flex items-center text-sm text-gray-600 mb-2">
                        <i className="fas fa-door-open mr-1"></i>
                        <span>{req.roomNumber}</span>
                        <i className="fas fa-building ml-3 mr-1"></i>
                        <span>{req.dormBuilding}</span>
                      </div>
                      <div className="flex items-center">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          req.status === RepairStatus.PENDING 
                            ? 'bg-red-100 text-red-700' 
                            : 'bg-yellow-100 text-yellow-700'
                        }`}>
                          {req.status}
                        </span>
                      </div>
                    </div>
                    <div className="ml-3">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    </div>
                  </div>
                </div>
              ))}
              {MOCK_REPAIR_REQUESTS.filter(r => r.status !== RepairStatus.COMPLETED && r.status !== RepairStatus.CONFIRMED).length > 3 && (
                <div className="mt-4 text-center">
                  <Link to="/repairs">
                    <Button variant="gradient" size="sm" rounded="full">
                      查看全部报修 <i className="fas fa-arrow-right ml-2"></i>
                    </Button>
                  </Link>
                </div>
              )}
              {MOCK_REPAIR_REQUESTS.filter(r => r.status !== RepairStatus.COMPLETED && r.status !== RepairStatus.CONFIRMED).length === 0 && (
                <div className="text-center py-8">
                  <i className="fas fa-check-circle text-4xl text-green-300 mb-3"></i>
                  <p className="text-gray-500">暂无待处理报修</p>
                </div>
              )}
            </div>
          </Card>
        )}
      </div>
    </div>
  );
};

export default DashboardPage;