-- 易宿管智能宿舍管理系统数据库
-- 数据库名: redhat
-- 创建时间: 2025-06-15

-- 创建数据库
CREATE DATABASE IF NOT EXISTS redhat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE redhat;

-- 1. 学院表
CREATE TABLE colleges (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. 专业表
CREATE TABLE majors (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    college_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE CASCADE,
    UNIQUE KEY unique_major_college (name, college_id)
);

-- 3. 宿舍楼表
CREATE TABLE dorm_buildings (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    floors INT NOT NULL,
    total_rooms INT NOT NULL,
    assigned_admin_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 4. 用户表
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role ENUM('系统管理员', '宿舍管理员', '学生', '维修人员') NOT NULL,
    college_id VARCHAR(50),
    major_id VARCHAR(50),
    dorm_building_id VARCHAR(50),
    room_number VARCHAR(20),
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE SET NULL,
    FOREIGN KEY (major_id) REFERENCES majors(id) ON DELETE SET NULL,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE SET NULL
);

-- 5. 房间表
CREATE TABLE rooms (
    id VARCHAR(50) PRIMARY KEY,
    room_number VARCHAR(20) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    floor INT NOT NULL,
    type ENUM('单人间', '双人间', '四人间', '六人间') NOT NULL,
    capacity INT NOT NULL,
    occupied_beds INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    UNIQUE KEY unique_room_building (room_number, dorm_building_id)
);

-- 6. 床位表
CREATE TABLE beds (
    id VARCHAR(50) PRIMARY KEY,
    room_id VARCHAR(50) NOT NULL,
    bed_number VARCHAR(10) NOT NULL,
    status ENUM('空闲', '已入住') DEFAULT '空闲',
    student_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_bed_room (bed_number, room_id)
);

-- 7. 维修请求表
CREATE TABLE repair_requests (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    room_id VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消') DEFAULT '待处理',
    assigned_staff_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_staff_id) REFERENCES users(id) ON DELETE SET NULL
);

-- 8. 公告表
CREATE TABLE announcements (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    author_id VARCHAR(50) NOT NULL,
    target_roles JSON,
    is_urgent BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 9. 水电费账单表
CREATE TABLE utility_bills (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    room_id VARCHAR(50) NOT NULL,
    month VARCHAR(7) NOT NULL, -- YYYY-MM格式
    electricity_usage DECIMAL(10,2) NOT NULL,
    electricity_cost DECIMAL(10,2) NOT NULL,
    water_usage DECIMAL(10,2) NOT NULL,
    water_cost DECIMAL(10,2) NOT NULL,
    total_cost DECIMAL(10,2) NOT NULL,
    is_paid BOOLEAN DEFAULT FALSE,
    paid_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_month (student_id, month)
);

-- 10. 违规记录表
CREATE TABLE violations (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    action_taken TEXT,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 11. 晚归记录表
CREATE TABLE late_returns (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    time TIME NOT NULL,
    reason TEXT NOT NULL,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 12. 访客记录表
CREATE TABLE visitors (
    id VARCHAR(50) PRIMARY KEY,
    visitor_name VARCHAR(100) NOT NULL,
    visitor_id_number VARCHAR(50),
    reason TEXT NOT NULL,
    entry_time TIMESTAMP NOT NULL,
    exit_time TIMESTAMP NULL,
    visited_student_id VARCHAR(50) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (visited_student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 13. 文明宿舍评分表
CREATE TABLE civilized_dorm_scores (
    id VARCHAR(50) PRIMARY KEY,
    dorm_building_id VARCHAR(50) NOT NULL,
    room_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    score INT NOT NULL CHECK (score >= 0 AND score <= 100),
    notes TEXT,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 添加外键约束到宿舍楼表
ALTER TABLE dorm_buildings ADD FOREIGN KEY (assigned_admin_id) REFERENCES users(id) ON DELETE SET NULL;

-- 创建索引以提高查询性能
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_dorm_building ON users(dorm_building_id);
CREATE INDEX idx_repair_requests_status ON repair_requests(status);
CREATE INDEX idx_repair_requests_student ON repair_requests(student_id);
CREATE INDEX idx_utility_bills_student_month ON utility_bills(student_id, month);
CREATE INDEX idx_violations_student ON violations(student_id);
CREATE INDEX idx_late_returns_student ON late_returns(student_id);
CREATE INDEX idx_visitors_student ON visitors(visited_student_id);
CREATE INDEX idx_civilized_scores_room ON civilized_dorm_scores(room_id);

-- 插入初始数据

-- 插入学院数据（与前端模拟数据对应）
INSERT INTO colleges (id, name) VALUES
('col01', '工程学院'),
('col02', '文理学院'),
('col03', '商学院');

-- 插入专业数据（与前端模拟数据对应）
INSERT INTO majors (id, name, college_id) VALUES
('maj01', '计算机科学', 'col01'),
('maj02', '机械工程', 'col01'),
('maj03', '历史学', 'col02'),
('maj04', '文学', 'col02'),
('maj05', '金融学', 'col03');

-- 插入宿舍楼数据（与前端模拟数据对应）
INSERT INTO dorm_buildings (id, name, floors, total_rooms) VALUES
('bldgA', 'A栋 (阿尔法楼)', 5, 50),
('bldgB', 'B栋 (贝塔公寓)', 4, 40),
('bldgC', 'C栋 (伽马学舍)', 6, 60);

-- 插入用户数据（使用bcrypt加密密码，对应password123）
INSERT INTO users (id, name, email, password, role) VALUES
('sysadmin01', '系统管理员用户', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RX.PZpDvK', '系统管理员');

INSERT INTO users (id, name, email, password, role, dorm_building_id) VALUES
('dormadmin01', '张三 (A栋宿舍管理员)', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RX.PZpDvK', '宿舍管理员', 'bldgA'),
('dormadmin02', '李四 (B栋宿舍管理员)', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RX.PZpDvK', '宿舍管理员', 'bldgB');

INSERT INTO users (id, name, email, password, phone, role, college_id, major_id, dorm_building_id, room_number, emergency_contact_name, emergency_contact_phone) VALUES
('student01', '王五', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RX.PZpDvK', '13000000001', '学生', 'col01', 'maj01', 'bldgA', '101', '王大锤', '13800138000'),
('student02', '赵六', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RX.PZpDvK', '13000000002', '学生', 'col02', 'maj03', 'bldgB', '205', '赵铁柱', '13900139000'),
('student03', '孙七', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RX.PZpDvK', '13000000003', '学生', 'col01', 'maj02', 'bldgA', '102', '孙悟空', '13700137000');

INSERT INTO users (id, name, email, password, role) VALUES
('repair01', '维修工丁师傅', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RX.PZpDvK', '维修人员');

-- 更新宿舍楼管理员分配
UPDATE dorm_buildings SET assigned_admin_id = 'dormadmin01' WHERE id = 'bldgA';
UPDATE dorm_buildings SET assigned_admin_id = 'dormadmin02' WHERE id = 'bldgB';

-- 插入房间数据（与前端模拟数据对应）
INSERT INTO rooms (id, room_number, dorm_building_id, floor, type, capacity, occupied_beds) VALUES
('roomA101', '101', 'bldgA', 1, '六人间', 6, 1),
('roomA102', '102', 'bldgA', 1, '双人间', 2, 1),
('roomB205', '205', 'bldgB', 2, '六人间', 6, 1),
('roomC301', '301', 'bldgC', 3, '单人间', 1, 0);

-- 插入床位数据（与前端模拟数据对应）
INSERT INTO beds (id, room_id, bed_number, status, student_id) VALUES
('bedA101-1', 'roomA101', '1', '已入住', 'student01'),
('bedA101-2', 'roomA101', '2', '空闲', NULL),
('bedA101-3', 'roomA101', '3', '空闲', NULL),
('bedA101-4', 'roomA101', '4', '空闲', NULL),
('bedA101-5', 'roomA101', '5', '空闲', NULL),
('bedA101-6', 'roomA101', '6', '空闲', NULL),
('bedA102-1', 'roomA102', '1', '已入住', 'student03'),
('bedA102-2', 'roomA102', '2', '空闲', NULL),
('bedB205-1', 'roomB205', '1', '已入住', 'student02'),
('bedB205-2', 'roomB205', '2', '空闲', NULL),
('bedB205-3', 'roomB205', '3', '空闲', NULL),
('bedB205-4', 'roomB205', '4', '空闲', NULL),
('bedB205-5', 'roomB205', '5', '空闲', NULL),
('bedB205-6', 'roomB205', '6', '空闲', NULL),
('bedC301-1', 'roomC301', '1', '空闲', NULL);

-- 插入维修请求数据（与前端模拟数据对应）
INSERT INTO repair_requests (id, student_id, room_id, title, description, status, assigned_staff_id, created_at, completed_at) VALUES
('repair001', 'student01', 'roomA101', '卫生间水龙头漏水', '卫生间水龙头漏水严重，无法关闭。', '已指派', 'repair01', '2024-07-20 10:00:00', NULL),
('repair002', 'student02', 'roomB205', '书桌台灯不亮', '书桌台灯不亮，更换灯泡无效。', '待处理', NULL, '2024-07-21 14:30:00', NULL),
('repair003', 'student03', 'roomA102', '空调制冷效果差', '空调制冷效果差，噪音大。', '维修中', 'repair01', '2024-07-22 08:15:00', NULL),
('repair004', 'student01', 'roomA101', '窗户卡住', '窗户卡住，无法完全关闭。', '已完成', 'repair01', '2024-07-19 16:00:00', '2024-07-20 09:30:00');

-- 插入公告数据（与前端模拟数据对应）
INSERT INTO announcements (id, title, content, author_id, target_roles, is_urgent, created_at) VALUES
('anno001', '近期维修安排通知', '请注意，A栋将于7月25日上午9点至下午5点进行计划内维修。', 'sysadmin01', '["学生", "宿舍管理员"]', FALSE, '2024-07-19 08:00:00'),
('anno002', '宿舍会议 - B栋', 'B栋全体住宿学生务必参加7月26日晚上7点在公共活动室举行的会议。', 'dormadmin02', '["学生"]', FALSE, '2024-07-20 11:00:00');

-- 插入水电费账单数据（与前端模拟数据对应）
INSERT INTO utility_bills (id, student_id, room_id, month, electricity_usage, electricity_cost, water_usage, water_cost, total_cost, is_paid, paid_at) VALUES
('bill001', 'student01', 'roomA101', '2024-06', 50.0, 25.0, 5.0, 10.0, 35.0, TRUE, '2024-07-05 10:30:00'),
('bill002', 'student02', 'roomB205', '2024-06', 60.0, 30.0, 6.0, 12.0, 42.0, FALSE, NULL),
('bill003', 'student01', 'roomA101', '2024-05', 45.0, 22.5, 4.0, 8.0, 30.5, FALSE, NULL),
('bill004', 'student03', 'roomA102', '2024-06', 30.0, 15.0, 3.0, 6.0, 21.0, TRUE, '2024-07-03 14:20:00');

-- 插入违规记录数据（与前端模拟数据对应）
INSERT INTO violations (id, student_id, dorm_building_id, date, type, description, action_taken, recorded_by) VALUES
('vio001', 'student01', 'bldgA', '2024-07-15', '违规电器', '宿舍内使用大功率吹风机', '口头警告', 'dormadmin01'),
('vio002', 'student02', 'bldgB', '2024-07-16', '晚归', '超过规定时间返校', '记录一次', 'dormadmin02');

-- 插入晚归记录数据（与前端模拟数据对应）
INSERT INTO late_returns (id, student_id, dorm_building_id, date, time, reason, recorded_by) VALUES
('lr001', 'student01', 'bldgA', '2024-07-18', '23:30:00', '图书馆学习', 'dormadmin01'),
('lr002', 'student03', 'bldgA', '2024-07-19', '00:15:00', '社团活动', 'dormadmin01');

-- 插入访客记录数据（与前端模拟数据对应）
INSERT INTO visitors (id, visitor_name, visitor_id_number, reason, entry_time, exit_time, visited_student_id, dorm_building_id, recorded_by) VALUES
('vis001', '访客甲', '123456789012345X', '探亲', '2024-07-20 14:00:00', '2024-07-20 16:00:00', 'student01', 'bldgA', 'dormadmin01'),
('vis002', '访客乙', '987654321098765Y', '送东西', '2024-07-21 10:00:00', NULL, 'student02', 'bldgB', 'dormadmin02');

-- 插入文明宿舍评分数据（与前端模拟数据对应）
INSERT INTO civilized_dorm_scores (id, dorm_building_id, room_id, date, score, notes, recorded_by) VALUES
('cds001', 'bldgA', 'roomA101', '2024-07-01', 95, '卫生良好，物品摆放整齐。', 'dormadmin01'),
('cds002', 'bldgB', 'roomB205', '2024-07-01', 88, '阳台有杂物，已提醒。', 'dormadmin02'),
('cds003', 'bldgA', 'roomA102', '2024-07-01', 92, '整体不错。', 'dormadmin01');
